const Subscriber = require("../models/Subscriber");
const crypto = require("crypto");

// Subscribe to newsletter
exports.subscribe = async (req, res) => {
  try {
    const { email, name } = req.body;

    // Validate email
    if (!email || !email.trim()) {
      return res.status(400).json({ message: "Email is required" });
    }

    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(email.trim())) {
      return res.status(400).json({ message: "Please provide a valid email address" });
    }

    // Check if email already exists
    const existingSubscriber = await Subscriber.findOne({ 
      email: email.trim().toLowerCase() 
    });

    if (existingSubscriber) {
      if (existingSubscriber.isActive) {
        return res.status(400).json({ 
          message: "This email is already subscribed to our newsletter" 
        });
      } else {
        // Reactivate subscription
        existingSubscriber.isActive = true;
        existingSubscriber.subscribedAt = new Date();
        existingSubscriber.unsubscribedAt = undefined;
        existingSubscriber.status = 1;
        
        if (name && name.trim()) {
          existingSubscriber.name = name.trim();
        }

        await existingSubscriber.save();

        return res.status(200).json({
          message: "Welcome back! Your subscription has been reactivated.",
          subscriber: {
            email: existingSubscriber.email,
            name: existingSubscriber.name,
            subscribedAt: existingSubscriber.subscribedAt
          }
        });
      }
    }

    // Generate unsubscribe token
    const unsubscribeToken = crypto.randomBytes(32).toString('hex');

    // Create new subscriber
    const newSubscriber = new Subscriber({
      email: email.trim().toLowerCase(),
      name: name ? name.trim() : undefined,
      unsubscribeToken,
      isActive: true,
      subscribedAt: new Date(),
      status: 1
    });

    await newSubscriber.save();

    res.status(201).json({
      message: "Successfully subscribed to our newsletter! You'll receive updates about new courses and announcements.",
      subscriber: {
        email: newSubscriber.email,
        name: newSubscriber.name,
        subscribedAt: newSubscriber.subscribedAt
      }
    });

  } catch (error) {
    console.error("Error subscribing to newsletter:", error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({ 
        message: "This email is already subscribed to our newsletter" 
      });
    }

    res.status(500).json({
      message: "Error subscribing to newsletter. Please try again later.",
      error: error.message,
    });
  }
};

// Unsubscribe from newsletter
exports.unsubscribe = async (req, res) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).json({ message: "Unsubscribe token is required" });
    }

    const subscriber = await Subscriber.findOne({ 
      unsubscribeToken: token,
      isActive: true,
      status: 1
    });

    if (!subscriber) {
      return res.status(404).json({ 
        message: "Invalid unsubscribe link or already unsubscribed" 
      });
    }

    // Deactivate subscription
    subscriber.isActive = false;
    subscriber.unsubscribedAt = new Date();
    await subscriber.save();

    res.status(200).json({
      message: "You have been successfully unsubscribed from our newsletter.",
      email: subscriber.email
    });

  } catch (error) {
    console.error("Error unsubscribing from newsletter:", error);
    res.status(500).json({
      message: "Error processing unsubscribe request. Please try again later.",
      error: error.message,
    });
  }
};

// Get all active subscribers (admin only)
exports.getSubscribers = async (req, res) => {
  try {
    const { page = 1, limit = 10, search } = req.query;
    
    const query = { 
      isActive: true, 
      status: 1 
    };

    // Add search functionality
    if (search && search.trim()) {
      query.$or = [
        { email: { $regex: search.trim(), $options: 'i' } },
        { name: { $regex: search.trim(), $options: 'i' } }
      ];
    }

    const subscribers = await Subscriber.find(query)
      .select('email name subscribedAt')
      .sort({ subscribedAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Subscriber.countDocuments(query);

    res.status(200).json({
      subscribers,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });

  } catch (error) {
    console.error("Error retrieving subscribers:", error);
    res.status(500).json({
      message: "Error retrieving subscribers",
      error: error.message,
    });
  }
};

// Get subscription statistics (admin only)
exports.getSubscriptionStats = async (req, res) => {
  try {
    const totalSubscribers = await Subscriber.countDocuments({ 
      isActive: true, 
      status: 1 
    });
    
    const totalUnsubscribed = await Subscriber.countDocuments({ 
      isActive: false, 
      status: 1 
    });

    // Get subscribers from last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentSubscribers = await Subscriber.countDocuments({
      isActive: true,
      status: 1,
      subscribedAt: { $gte: thirtyDaysAgo }
    });

    res.status(200).json({
      totalSubscribers,
      totalUnsubscribed,
      recentSubscribers,
      totalEverSubscribed: totalSubscribers + totalUnsubscribed
    });

  } catch (error) {
    console.error("Error retrieving subscription statistics:", error);
    res.status(500).json({
      message: "Error retrieving subscription statistics",
      error: error.message,
    });
  }
};
