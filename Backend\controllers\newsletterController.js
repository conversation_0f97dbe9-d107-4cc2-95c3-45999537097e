const Newsletter = require('../models/Newsletter');
const Subscriber = require('../models/Subscriber');
const { sendNewsletterToSubscribers } = require('../services/emailService');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');

// Get all newsletters (admin only)
exports.getAllNewsletters = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, type, status } = req.query;
  
  const query = {};
  if (type) query.type = type;
  if (status) query.status = status;

  const newsletters = await Newsletter.find(query)
    .populate('createdBy', 'name email')
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);

  const total = await Newsletter.countDocuments(query);

  res.status(200).json({
    status: 'success',
    data: {
      newsletters,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// Get newsletter by ID
exports.getNewsletter = catchAsync(async (req, res, next) => {
  const newsletter = await Newsletter.findById(req.params.id)
    .populate('createdBy', 'name email');

  if (!newsletter) {
    return next(new AppError('Newsletter not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: { newsletter }
  });
});

// Create newsletter
exports.createNewsletter = catchAsync(async (req, res, next) => {
  const { title, content, type, relatedId, relatedModel } = req.body;

  const newsletter = await Newsletter.create({
    title,
    content,
    type,
    relatedId,
    relatedModel,
    createdBy: req.user._id
  });

  res.status(201).json({
    status: 'success',
    data: { newsletter }
  });
});

// Update newsletter
exports.updateNewsletter = catchAsync(async (req, res, next) => {
  const newsletter = await Newsletter.findById(req.params.id);

  if (!newsletter) {
    return next(new AppError('Newsletter not found', 404));
  }

  // Only allow updates if newsletter hasn't been sent
  if (newsletter.status === 'sent') {
    return next(new AppError('Cannot update a newsletter that has already been sent', 400));
  }

  const updatedNewsletter = await Newsletter.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  ).populate('createdBy', 'name email');

  res.status(200).json({
    status: 'success',
    data: { newsletter: updatedNewsletter }
  });
});

// Delete newsletter
exports.deleteNewsletter = catchAsync(async (req, res, next) => {
  const newsletter = await Newsletter.findById(req.params.id);

  if (!newsletter) {
    return next(new AppError('Newsletter not found', 404));
  }

  await Newsletter.findByIdAndDelete(req.params.id);

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Send newsletter to subscribers
exports.sendNewsletter = catchAsync(async (req, res, next) => {
  const newsletter = await Newsletter.findById(req.params.id);

  if (!newsletter) {
    return next(new AppError('Newsletter not found', 404));
  }

  if (newsletter.status === 'sent') {
    return next(new AppError('Newsletter has already been sent', 400));
  }

  const result = await sendNewsletterToSubscribers(req.params.id);

  res.status(200).json({
    status: 'success',
    message: `Newsletter sent to ${result.sent} subscribers`,
    data: result
  });
});

// Get all subscribers (admin only)
exports.getAllSubscribers = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, isActive } = req.query;
  
  const query = { status: 1 }; // Only active subscribers
  if (isActive !== undefined) query.isActive = isActive === 'true';

  const subscribers = await Subscriber.find(query)
    .sort({ subscribedAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);

  const total = await Subscriber.countDocuments(query);

  res.status(200).json({
    status: 'success',
    data: {
      subscribers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// Get subscriber statistics
exports.getSubscriberStats = catchAsync(async (req, res, next) => {
  const totalSubscribers = await Subscriber.countDocuments({ status: 1 });
  const activeSubscribers = await Subscriber.countDocuments({ status: 1, isActive: true });
  const inactiveSubscribers = await Subscriber.countDocuments({ status: 1, isActive: false });

  // Get subscription trends (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const recentSubscribers = await Subscriber.countDocuments({
    status: 1,
    subscribedAt: { $gte: thirtyDaysAgo }
  });

  res.status(200).json({
    status: 'success',
    data: {
      total: totalSubscribers,
      active: activeSubscribers,
      inactive: inactiveSubscribers,
      recentSubscriptions: recentSubscribers
    }
  });
});

// Auto-create and send newsletter for new content
exports.autoCreateNewsletter = async (contentType, contentData) => {
  try {
    let title, content, type, relatedId, relatedModel;

    switch (contentType) {
      case 'course':
        title = `New Course Available: ${contentData.title}`;
        content = `
          <h3>New Course: ${contentData.title}</h3>
          <p>${contentData.description}</p>
          <p>Enroll now to start learning!</p>
        `;
        type = 'course';
        relatedId = contentData._id;
        relatedModel = 'Course';
        break;

      case 'blog':
        title = `New Blog Post: ${contentData.title}`;
        content = `
          <h3>New Blog Post: ${contentData.title}</h3>
          <p>${contentData.shortDescription}</p>
          <p>Read the full article on our website.</p>
        `;
        type = 'blog';
        relatedId = contentData._id;
        relatedModel = 'Blog';
        break;

      case 'forum':
        title = `New Discussion: ${contentData.title}`;
        content = `
          <h3>New Forum Discussion: ${contentData.title}</h3>
          <p>Join the conversation and share your thoughts!</p>
        `;
        type = 'forum';
        relatedId = contentData._id;
        relatedModel = 'Forum';
        break;

      default:
        return;
    }

    // Create newsletter
    const newsletter = await Newsletter.create({
      title,
      content,
      type,
      relatedId,
      relatedModel,
      createdBy: contentData.createdBy || contentData.author
    });

    // Send newsletter automatically
    await sendNewsletterToSubscribers(newsletter._id);

    console.log(`Newsletter sent for new ${contentType}: ${title}`);
  } catch (error) {
    console.error(`Failed to send newsletter for ${contentType}:`, error);
  }
};
