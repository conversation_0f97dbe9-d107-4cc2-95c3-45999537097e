const mongoose = require("mongoose");

const forumSchema = new mongoose.Schema(
  {
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Course",
      required: true,
    },
    threads: [
      {
        title: { type: String, required: true },
        content: { type: String, required: true },
        author: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
          required: true,
        },
        isAnnouncement: { type: Boolean, default: false },
        isPinned: { type: Boolean, default: false },
        isQuestion: { type: Boolean, default: false },
        isSolved: { type: Boolean, default: false },
        tags: [{ type: String }],
        views: { type: Number, default: 0 },
        createdAt: { type: Date, default: Date.now },
        updatedAt: { type: Date, default: Date.now },
        replies: [
          {
            content: { type: String, required: true },
            author: {
              type: mongoose.Schema.Types.ObjectId,
              ref: "User",
              required: true,
            },
            isAnswer: { type: Boolean, default: false },
            createdAt: { type: Date, default: Date.now },
            updatedAt: { type: Date, default: Date.now },
            likes: [
              {
                user: {
                  type: mongoose.Schema.Types.ObjectId,
                  ref: "User",
                },
              },
            ],
          },
        ],
        likes: [
          {
            user: {
              type: mongoose.Schema.Types.ObjectId,
              ref: "User",
            },
          },
        ],
      },
    ],
    status: { type: Number, default: 1 }, // 1: active, 0: inactive (soft delete)
  },
  { timestamps: true }
);

// Create index for faster queries
forumSchema.index({ course: 1 });

module.exports = mongoose.model("Forum", forumSchema);
