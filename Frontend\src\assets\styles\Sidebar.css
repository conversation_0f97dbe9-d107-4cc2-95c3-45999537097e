.sidebar-layout-parent .sidebar {
  width: 280px;
  height: 100vh;
  background: var(--bg-white);
  box-shadow: var(--box-shadow-light);
  position: fixed;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
  z-index: 999;
  display: flex;
  flex-direction: column;
}

/* Sidebar header */
.sidebar-layout-parent .sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 13px 10px;
  border-bottom: 1px solid var(--border-gray);
  justify-content: center;
}
.sidebar-layout-parent .sidebar-header img {
  width: 40px;
  margin-right: 1rem;
}
.sidebar-layout-parent .sidebar-logo {
  color: var(--primary-color);
  font-size: var(--heading4);
  font-weight: 700;
  margin: 0;
  font-family: var(--font-Coiny);
}

.sidebar-layout-parent .close-sidebar-btn {
  display: none; /* Hidden on desktop */
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-gray);
}

/* Navigation menu */
.sidebar-layout-parent .sidebar-nav {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
  margin-bottom: 20px;
}
.sidebar-layout-parent .submenu-icon {
  margin-right: 10px;
}
.sidebar-layout-parent .sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-layout-parent .menu-item {
  margin-bottom: 5px !important;
}

.sidebar-layout-parent .menu-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--text-gray);
  text-decoration: none;
  border-radius: var(--border-small-radius);
  margin: 0 10px;
  transition: all 0.2s ease;
}

.sidebar-layout-parent .menu-link:hover {
  background-color: var(--bg-gray);
  color: var(--primary-color);
}

.sidebar-layout-parent .menu-link.active {
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
  font-weight: 600;
}

.sidebar-layout-parent .menu-icon {
  font-size: 20px;
  margin-right: 15px;
}

.sidebar-layout-parent .menu-text {
  font-size: var(--smallfont);
}
.sidebar-layout-parent .submenu-text {
  font-size: var(--smallfont);
}

/* Overlay for mobile */
.sidebar-layout-parent .sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* Dropdown menu styles */
.sidebar-layout-parent .dropdown-menu {
  margin: 0 10px;
}

.sidebar-layout-parent .dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  color: var(--text-gray);
  background: none;
  border: none;
  border-radius: var(--border-small-radius);
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sidebar-layout-parent .dropdown-toggle:hover {
  background-color: var(--bg-gray);
  color: var(--primary-color);
}

.sidebar-layout-parent .dropdown-toggle.active {
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
  font-weight: 600;
}

.sidebar-layout-parent .dropdown-toggle .menu-icon-wrapper {
  display: flex;
  align-items: center;
}

.sidebar-layout-parent .dropdown-toggle .menu-icon {
  font-size: 20px;
  margin-right: 15px;
}

.sidebar-layout-parent .dropdown-toggle .menu-text {
  font-size: var(--smallfont);
}

.sidebar-layout-parent .dropdown-toggle .dropdown-arrow {
  transition: transform 0.3s ease;
}

.sidebar-layout-parent .dropdown-toggle.open .dropdown-arrow {
  transform: rotate(180deg);
}

.sidebar-layout-parent .dropdown-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.sidebar-layout-parent .dropdown-content.open {
  max-height: 200px;
}

.sidebar-layout-parent .submenu-item {
  padding-left: 55px;
  margin: 5px 0;
}

.sidebar-layout-parent .submenu-link {
  display: flex;
  align-items: center;
  padding: 8px 0;
  color: var(--text-gray);
  text-decoration: none;
  border-radius: var(--border-small-radius);
  transition: all 0.2s ease;
}

.sidebar-layout-parent .submenu-link:hover {
  color: var(--primary-color);
}

.sidebar-layout-parent .submenu-link.active {
  color: var(--primary-color);
  font-weight: 600;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .sidebar-layout-parent .sidebar {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .sidebar-layout-parent .sidebar {
    width: 280px;
    transform: translateX(-100%);
  }

  .sidebar-layout-parent .sidebar.open {
    transform: translateX(0);
  }

  .sidebar-layout-parent .sidebar-overlay {
    display: block;
  }

  .sidebar-layout-parent .close-sidebar-btn {
    display: block;
  }
  .sidebar-layout-parent .sidebar-header {
    position: relative;
    justify-content: flex-start;
  }
  .sidebar-layout-parent .close-sidebar-btn {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translate(0%, -50%);
  }
}

/* Newsletter Section */
.sidebar-layout-parent .sidebar-newsletter {
  padding: 0 10px 20px 10px;
  margin-top: auto;
}

@media (max-width: 768px) {
  .sidebar-layout-parent .sidebar-newsletter {
    padding: 0 15px 15px 15px;
  }
}