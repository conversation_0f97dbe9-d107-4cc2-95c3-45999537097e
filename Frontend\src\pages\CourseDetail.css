/* CourseDetail.css */
/* Course Detail Page Styles using variables from index.css */

.course-detail-container {
  margin: 0 auto;
  min-height: 100vh;
  color: var(--text-color);
}

/* Loading and Error States */
.course-detail-loading,
.course-detail-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 30px;
}

.course-detail-container .loading-spinner {
  border: 4px solid rgba(var(--primary-color-rgb), 0.1);
  border-left-color: var(--primary-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.course-detail-error button {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-medium-radius);
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}
.course-detail-error button:hover {
  background-color: var(--primary-light-color);
}

/* Course Header */
.course-detail-container .course-detail-header {
  margin-bottom: 30px;
  padding: 30px;
  background-color: var(--bg-primary);
  border-radius: var(--border-large-radius);
  box-shadow: var(--box-shadow-light);
}

.course-detail-container .course-header-main {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.course-detail-container .course-thumbnail-header {
  flex-shrink: 0;
}

.course-detail-container .course-header-thumbnail {
  width: 80px;
 aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: var(--border-medium-radius);
  box-shadow: var(--box-shadow-light);
}

.course-detail-container .course-title-section {
  flex: 1;
}

.course-detail-container .course-title {
  font-size: var(--heading3);
  color: var(--white);
  margin-bottom: 15px;
  font-weight: 600;
}

.course-detail-container .course-meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  font-size: var(--smallfont);
  color: white;
}

.course-detail-container .course-meta-info span {
  display: flex;
  align-items: center;
  color: var(--white);
}

.course-detail-container .course-rating {
  display: flex;
  align-items: center;
}

.course-detail-container .star-icon {
  color: #ffc107;
  margin-right: 5px;
}

/* Course Body Layout */
.course-detail-container .course-detail-body {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.course-detail-container .course-main-content {
  flex: 1;
  width: 100%;
}

.course-detail-container .course-sidebar {
  width: 350px;
}

/* Course Tabs */
.course-detail-container .course-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-gray);
  margin-bottom: 20px;
}

.course-detail-container .tab-button {
  padding: 15px 20px;
  background: none;
  border: none;
  font-size: var(--basefont);
  color: var(--text-gray);
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.course-detail-container .tab-button:hover {
  color: var(--primary-color);
}

.course-detail-container .tab-button.active {
  color: var(--primary-color);
  font-weight: 600;
}

.course-detail-container .tab-button.active:after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
}

.course-detail-container .tab-icon {
  font-size: 16px;
}

/* Tab Content */
.course-detail-container .tab-content {
  padding: 20px 0;
}

/* Information Tab */
.course-detail-container .information-tab {
  line-height: 1.6;
}

.course-detail-container .course-description {
  margin-bottom: 30px;
}

.course-detail-container .course-description p {
  margin-bottom: 20px;
  line-height: 1.7;
  text-align: justify;
}

.course-detail-container .learning-outcomes,
.course-detail-container .requirements {
  margin-bottom: 30px;
}

.course-detail-container .learning-outcomes h3,
.course-detail-container .requirements h3 {
  font-size: var(--heading5);
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 600;
}

.course-detail-container .learning-outcomes ul,
.course-detail-container .requirements ul {
  padding-left: 25px;
}

.course-detail-container .learning-outcomes li,
.course-detail-container .requirements li {
  margin-bottom: 10px;
}

.course-detail-container .course-details {
  background-color: var(--primary-light-bg);
  padding: 20px;
  border-radius: var(--border-medium-radius);
  margin-bottom: 30px;
}

.course-detail-container .course-details p {
  margin-bottom: 10px;
}

/* Course Modules List in Information Tab */
.course-detail-container .course-modules-list {
  margin-top: 30px;
}

.course-detail-container .course-modules-list h3 {
  font-size: var(--heading5);
  color: var(--primary-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.course-detail-container .modules-accordion {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.course-detail-container .module-item {
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  overflow: hidden;
  transition: all 0.3s ease;
}

.course-detail-container .module-item:hover {
  box-shadow: var(--box-shadow-light);
}

.course-detail-container .module-header {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-white);
}

.course-detail-container .module-header h4 {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  margin-right: 20px;
}

.course-detail-container .module-meta {
  color: var(--text-gray);
  font-size: var(--smallfont);
  margin-right: auto;
}

.course-detail-container .preview-module-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.course-detail-container .preview-module-btn:hover {
  background-color: var(--primary-hover-color);
}

.course-detail-container .no-modules-message {
  color: var(--text-gray);
  text-align: center;
  padding: 20px;
  background-color: var(--bg-gray);
  border-radius: var(--border-medium-radius);
}

/* Instructor Info */
.course-detail-container .instructor-info {
  margin-bottom: 30px;
  padding: 20px;
  background-color: var(--white);
  border-radius: var(--border-medium-radius);
}

.course-detail-container .instructor-profile {
  display: flex;
  align-items: flex-start;
}

.course-detail-container .instructor-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  border: 3px solid var(--primary-light-color);
}

.course-detail-container .instructor-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-detail-container .instructor-details h4 {
  font-size: var(--heading6);
  color: var(--text-color);
  margin-bottom: 5px;
}

.course-detail-container .instructor-details p {
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.course-detail-container .instructor-role {
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 15px;
}

/* Enhanced Creator Information Styles */
.course-detail-container .creator-detailed-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.course-detail-container .creator-info-item {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.course-detail-container .creator-info-item strong {
  color: var(--text-color);
  font-weight: 600;
  min-width: 100px;
}

.course-detail-container .creator-info-item span {
  color: var(--text-gray);
  flex: 1;
}

.course-detail-container .creator-info-item.bio {
  flex-direction: column;
  align-items: flex-start;
}

.course-detail-container .creator-bio {
  margin-top: 5px;
  color: var(--text-gray);
  line-height: 1.5;
  font-style: italic;
}

.course-detail-container .creator-social-links {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid var(--border-light);
}

.course-detail-container .social-links-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 8px;
}

.course-detail-container .social-link {
  padding: 5px 12px;
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  text-decoration: none;
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  transition: all 0.3s ease;
}

.course-detail-container .social-link:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-1px);
}

/* FAQs */
.course-detail-container .course-faqs {
  margin-top: 30px;
}

.course-detail-container .course-faqs h3 {
  font-size: var(--heading5);
  color: var(--primary-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.course-detail-container .faq-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.course-detail-container .faq-item {
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  overflow: hidden;
}

.course-detail-container .faq-question {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  background-color: var(--bg-white);
  transition: background-color 0.3s ease;
}

.course-detail-container .faq-question:hover {
  background-color: var(--bg-gray);
}

.course-detail-container .faq-question.active {
  background-color: var(--primary-light-bg);
}

.course-detail-container .faq-question h4 {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.course-detail-container .faq-arrow {
  transition: transform 0.3s ease;
  color: var(--text-gray);
}

.course-detail-container .faq-arrow.rotate {
  transform: rotate(180deg);
}

.course-detail-container .faq-answer {
  padding: 15px 20px;
  background-color: var(--bg-white);
  border-top: 1px solid var(--border-gray);
}

/* Reviews Tab */
.course-detail-container .reviews-tab {
  padding: 20px 0;
}

.course-detail-container .comments-section h3 {
  font-size: var(--heading5);
  color: var(--primary-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.course-detail-container .comment-input {
  margin-bottom: 30px;
}

.course-detail-container .comment-input textarea {
  width: 100%;
  height: 120px;
  padding: 15px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  resize: none;
  margin-bottom: 15px;
  font-family: var(--font-Poppins);
  font-size: var(--basefont);
}

.course-detail-container .comment-input textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.course-detail-container .post-comment-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.course-detail-container .post-comment-button:hover {
  background-color: var(--primary-hover-color);
}

/* Enrolled Users Tab */
.course-detail-container .enrolled-users-tab {
  padding: 20px 0;
}

.course-detail-container .enrolled-users-tab h3 {
  font-size: var(--heading5);
  color: var(--primary-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.course-detail-container .enrolled-users-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.course-detail-container .enrolled-user-card {
  background-color: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: var(--box-shadow-light);
}

.course-detail-container .enrolled-user-card:hover {
  box-shadow: var(--box-shadow);
  transform: translateY(-3px);
}

.course-detail-container .user-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.course-detail-container .user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-light-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.course-detail-container .user-icon {
  font-size: 22px;
  color: var(--primary-color);
   background-color: transparent !important;
   margin-right: 0 !important;
}

.course-detail-container .user-details h4 {
  font-size: var(--basefont);
  color: var(--text-color);
  margin-bottom: 5px;
  font-weight: 500;
}

.course-detail-container .status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: capitalize;
}

.course-detail-container .status-in_progress {
  background-color: #e3f2fd;
  color: #1976d2;
}

.course-detail-container .status-completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.course-detail-container .status-enrolled {
  background-color: #fff8e1;
  color: #f57f17;
}

.course-detail-container .user-progress {
  margin-bottom: 15px;
}

.course-detail-container .progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: var(--smallfont);
  color: var(--text-gray);
}

.course-detail-container .progress-bar {
  height: 8px;
  background-color: var(--bg-gray);
  border-radius: 4px;
  overflow: hidden;
}

.course-detail-container .progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px;
}

.course-detail-container .enrollment-dates {
  font-size: var(--smallfont);
  color: var(--text-gray);
  line-height: 1.5;
}

.course-detail-container .no-enrolled-users {
  padding: 30px;
  text-align: center;
  background-color: var(--bg-gray);
  border-radius: var(--border-medium-radius);
  color: var(--text-gray);
}

/* Course Sidebar */
.course-detail-container .course-info-card {
  background-color: var(--bg-white);
  border-radius: var(--border-medium-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.course-detail-container .course-preview {
  width: 100%;
}

.course-detail-container .course-preview-placeholder {
  width: 100%;
  aspect-ratio: 16 / 9;
  position: relative;
  overflow: hidden;
}

.course-detail-container .course-preview-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.course-detail-container .play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.8);
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.course-detail-container .play-button:hover {
  background-color: rgba(255, 255, 255, 1);
}

.course-detail-container .video-duration {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: var(--smallfont);
}

.course-detail-container .course-price-section {
  padding: 20px;
  border-bottom: 1px solid var(--border-gray);
}

.course-detail-container .course-price {
  font-size: var(--heading5);
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.course-detail-container .enroll-button {
  width: 100%;
  padding: 15px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.course-detail-container .enroll-button:hover:not(:disabled) {
  background-color: var(--primary-hover-color);
}

.course-detail-container .enroll-button:disabled {
  background-color: var(--text-gray);
  cursor: not-allowed;
}

.course-detail-container .course-stats {
  display: flex;
  padding: 20px;
}

.course-detail-container .stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0 5px;
}
.course-detail-container .stat-info {
  display: grid;
  gap: 5px;
  align-items: center;
  justify-items: center;
}
.comment-edit-textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-family: var(--font-Poppins);
  font-size: var(--basefont);
  resize: vertical;
  min-height: 100px;
  margin-bottom: 15px;
  background-color: var(--bg-gray);
  transition: border-color 0.3s ease;
  outline: none;
}
.course-detail-container .stat-icon {
  font-size: 24px;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.course-detail-container .stat-value {
  font-weight: 600;
  font-size: var(--smallfont);
  margin-bottom: 5px;
  color: var(--text-color);
}

.course-detail-container .stat-label {
  font-size: var(--smallfont);
  color: var(--text-gray);
}

/* Module Preview Modal */
.course-detail-container .module-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: 20px;
}

.course-detail-container .module-preview-content {
  background-color: var(--bg-white);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  border-radius: var(--border-medium-radius);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.course-detail-container .close-preview {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 28px;
  color: var(--text-gray);
  cursor: pointer;
  z-index: 2;
}

.course-detail-container .module-preview-content h2 {
  padding: 20px;
  margin: 0;
  background-color: var(--primary-color);
  color: white;
  font-size: var(--heading5);
  font-weight: 500;
}

.course-detail-container .module-content-display {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

.course-detail-container .preview-section {
  margin-bottom: 30px;
}

.course-detail-container .preview-section h3 {
  font-size: var(--heading6);
  margin-bottom: 15px;
  color: var(--primary-color);
  font-weight: 500;
  border-bottom: 1px solid var(--border-gray);
  padding-bottom: 10px;
}

.course-detail-container .module-content-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.course-detail-container .content-preview-item {
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
  overflow: hidden;
}

.course-detail-container .content-preview-header {
  padding: 15px;
  background-color: var(--bg-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-detail-container .content-preview-header h4 {
  font-size: var(--basefont);
  font-weight: 500;
  margin: 0;
}

.course-detail-container .content-type-badge {
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
  padding: 4px 10px;
  border-radius: 20px;
  font-size: var(--extrasmallfont);
  font-weight: 500;
}

.course-detail-container .content-description {
  padding: 15px;
  border-top: 1px solid var(--border-gray);
  font-size: var(--smallfont);
  color: var(--text-gray);
}

.course-detail-container .content-preview {
  padding: 15px;
}

.course-detail-container .video-preview,
.course-detail-container .youtube-preview {
  aspect-ratio: 16 / 9;
  width: 100%;
  margin-bottom: 15px;
}

.course-detail-container .video-preview video,
.course-detail-container .youtube-preview iframe {
  width: 100%;
  height: 100%;
  border-radius: var(--border-small-radius);
}

.course-detail-container .image-preview {
  text-align: center;
}

.course-detail-container .preview-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: var(--border-small-radius);
}

.course-detail-container .document-preview {
  padding: 15px;
  text-align: center;
}

.course-detail-container .document-link {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
  text-decoration: none;
  border-radius: var(--border-small-radius);
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.course-detail-container .document-link:hover {
  background-color: var(--primary-color);
  color: white;
}

.course-detail-container .document-icon {
  margin-right: 10px;
}

.course-detail-container .no-content-message {
  padding: 30px;
  text-align: center;
  background-color: var(--bg-gray);
  border-radius: var(--border-medium-radius);
  color: var(--text-gray);
}

/* Quiz Section */
.course-detail-container .module-quiz-section {
  margin-top: 30px;
}

.course-detail-container .quiz-preview {
  padding: 20px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-medium-radius);
}

.course-detail-container .quiz-description {
  margin-bottom: 20px;
  color: var(--text-gray);
}

.course-detail-container .quiz-questions-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.course-detail-container .quiz-question {
  border-bottom: 1px solid var(--border-gray);
  padding-bottom: 20px;
}

.course-detail-container .quiz-question h4 {
  margin-bottom: 15px;
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.course-detail-container .quiz-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.course-detail-container .quiz-option {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--bg-gray);
  border-radius: var(--border-small-radius);
}

.course-detail-container .quiz-option input {
  margin-right: 10px;
}

.course-detail-container .quiz-submit-container {
  margin-top: 20px;
  text-align: center;
}

.course-detail-container .quiz-submit-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.course-detail-container .quiz-submit-button:hover {
  background-color: var(--primary-hover-color);
}

.course-detail-container .quiz-results {
  text-align: center;
}

.course-detail-container .quiz-result-header {
  margin-bottom: 20px;
}

.course-detail-container .result-passed {
  color: #2e7d32;
}

.course-detail-container .result-failed {
  color: #d32f2f;
}

.course-detail-container .quiz-score-summary {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
}

.course-detail-container .score-item {
  text-align: center;
}

.course-detail-container .score-label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.course-detail-container .score-value {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
}

.course-detail-container .quiz-retry-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.course-detail-container .quiz-retry-button:hover {
  background-color: var(--primary-hover-color);
}

.course-detail-container .no-questions-message {
  padding: 20px;
  text-align: center;
  color: var(--text-gray);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .course-detail-container .course-detail-body {
    flex-direction: column;
  }

  .course-detail-container .course-sidebar {
    width: 100%;
    order: -1;
    margin-bottom: 30px;
  }
}

@media (max-width: 768px) {
  .course-detail-container .course-header-content {
    padding: 10px 0;
  }

  .course-detail-container .course-header-main {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .course-detail-container .course-header-thumbnail {
    width: 100px;
    height: 67px;
  }

  .course-detail-container .course-title {
    font-size: var(--heading4);
  }

  .course-detail-container .creator-detailed-info {
    margin-top: 10px;
    padding-top: 10px;
  }

  .course-detail-container .creator-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .course-detail-container .creator-info-item strong {
    min-width: auto;
  }

  .course-detail-container .social-links-container {
    justify-content: center;
  }

  .course-detail-container .course-meta-info {
    flex-direction: column;
    gap: 10px;
  }

  .course-detail-container .tab-button {
    padding: 10px 15px;
  }

  .course-detail-container .enrolled-users-list {
    grid-template-columns: 1fr;
  }

  .course-detail-container .module-preview-content {
    width: 95%;
  }
}

@media (max-width: 576px) {
  .course-detail-container {
    padding: 10px;
  }

  .course-detail-container .course-detail-header {
    padding: 20px;
  }

  .course-detail-container .course-tabs {
    overflow-x: auto;
    padding-bottom: 5px;
  }

  .course-detail-container .tab-button {
    white-space: nowrap;
  }

  .course-detail-container .quiz-score-summary {
    flex-direction: column;
    gap: 15px;
  }
}