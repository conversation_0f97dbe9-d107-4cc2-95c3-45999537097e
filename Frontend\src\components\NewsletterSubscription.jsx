import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FaBell, FaCheck, FaTimes } from 'react-icons/fa';
import '../assets/styles/NewsletterSubscription.css';
import { toast } from 'react-toastify';

const NewsletterSubscription = () => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const validateEmail = (email) => {
    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email.trim()) {
      toast.error('Email is required', {
        position: "bottom-right"
      });
      return;
    }

    if (!validateEmail(email.trim())) {
      toast.error('Please enter a valid email address', {
        position: "bottom-right"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/subscriber/subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: email.trim(),
          name: name.trim() || undefined
        })
      });

      const data = await response.json();

      if (response.ok) {
        setIsSubscribed(true);
        setEmail('');
        setName('');
        toast.success(data.message || 'Successfully subscribed to newsletter!', {
          position: "bottom-right"
        });
      } else {
        toast.error(data.message || 'Failed to subscribe. Please try again.', {
          position: "bottom-right"
        });
      }
    } catch (error) {
      console.error('Error subscribing to newsletter:', error);
      toast.error('Network error. Please check your connection and try again.', {
        position: "bottom-right"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setIsSubscribed(false);
    setEmail('');
    setName('');
  };

  return (
    <div className="newsletter-subscription">
      <div className="newsletter-header">
        <div className="newsletter-icon">
          <FaBell />
        </div>
        <h3>Stay Updated</h3>
      </div>

      {!isSubscribed ? (
        <>
          <p className="newsletter-description">
            Subscribe to our newsletter and get notified about new courses, 
            announcements, and educational content!
          </p>

          <form onSubmit={handleSubmit} className="newsletter-form">
            <div className="form-group">
              <input
                type="text"
                placeholder="Your name (optional)"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="newsletter-input"
                disabled={isSubmitting}
              />
            </div>

            <div className="form-group">
              <div className="input-wrapper">
                <FaEnvelope className="input-icon" />
                <input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="newsletter-input email-input"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>

            <button 
              type="submit" 
              className="newsletter-submit-btn"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="submit-spinner"></div>
                  Subscribing...
                </>
              ) : (
                <>
                  <FaEnvelope />
                  Subscribe Now
                </>
              )}
            </button>
          </form>

          <div className="newsletter-benefits">
            <h4>What you'll get:</h4>
            <ul>
              <li>
                <FaCheck className="benefit-icon" />
                New course announcements
              </li>
              <li>
                <FaCheck className="benefit-icon" />
                Educational content updates
              </li>
              <li>
                <FaCheck className="benefit-icon" />
                Special offers & discounts
              </li>
              <li>
                <FaCheck className="benefit-icon" />
                Learning tips & resources
              </li>
            </ul>
          </div>

          <p className="newsletter-privacy">
            We respect your privacy. Unsubscribe at any time.
          </p>
        </>
      ) : (
        <div className="newsletter-success">
          <div className="success-icon">
            <FaCheck />
          </div>
          <h4>Thank You!</h4>
          <p>
            You've successfully subscribed to our newsletter. 
            Check your email for a confirmation message.
          </p>
          <button 
            className="newsletter-reset-btn"
            onClick={handleReset}
          >
            Subscribe Another Email
          </button>
        </div>
      )}
    </div>
  );
};

export default NewsletterSubscription;
